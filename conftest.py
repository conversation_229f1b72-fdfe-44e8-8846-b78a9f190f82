from doctest import ELLIPSIS

from sybil import <PERSON>y<PERSON>
from sybil.parsers.markdown import PythonCodeBlockParser
from sybil.parsers.rest import DocTestParser


def setup(namespace: dict):
    import zangar

    namespace["z"] = zangar


pytest_collect_file = <PERSON>ybil(
    parsers=[
        DocTestParser(optionflags=ELLIPSIS),
        PythonCodeBlockParser(),
    ],
    patterns=["*.md", "*.py"],
    setup=setup,
).pytest()
