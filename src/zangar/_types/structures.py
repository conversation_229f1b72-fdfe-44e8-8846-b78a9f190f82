from __future__ import annotations

import copy
import typing as t
import warnings
from collections.abc import Callable, Iterable, Mapping
from operator import getitem

from zangar._core import <PERSON>hema, SchemaBase
from zangar._messages import DefaultMessage, get_message
from zangar.exceptions import ValidationError

from .base import TypeSchema

T = t.TypeVar("T")

_empty: t.Any = object()


class ZangarField(t.Generic[T]):
    _empty = _empty

    def __init__(
        self,
        schema: SchemaBase[T],
        *,
        alias: str | None = None,
    ) -> None:
        self._schema = schema
        self.__alias = alias
        self._required = True
        self._required_message = None
        self._default: Callable[[], T] | T = _empty

    def _get_default(self):
        if callable(self._default):
            return self._default()
        return self._default

    @property
    def _alias(self):
        return self.__alias

    def parse(self, value, /):
        return self._schema.parse(value)

    def optional(self, *, default: T | Callable[[], T] = _empty):
        self._required = False
        self._default = default
        return self

    def required(self, /, *, message=None):
        self._required = True
        if message is not None:
            self._required_message = message
        return self


class StructMethods(Schema[T]):
    def __init__(
        self,
        *args,
        name_to_alias: dict[str, str],
        **kwargs,
    ):
        super().__init__(*args, **kwargs)
        self.__name_to_alias = name_to_alias

    def ensure_fields(
        self,
        fieldnames: list[str],
        func: Callable[[T], bool],
        /,
        *,
        message: t.Any | Callable[[T], t.Any] = None,
    ):
        """Validate the fields.

        Args:
            fieldnames: The names of the fields to validate.
            func: The function to validate the fields.
            message: The error message to display when the validation fails.
        """

        def inner_func(value):
            if func(value):
                return True
            error = ValidationError()
            for fieldname in fieldnames:
                error._set_child_err(
                    self.__name_to_alias[fieldname],
                    ValidationError(
                        get_message(
                            (
                                message
                                if message is not None
                                else DefaultMessage(name="ensure_failed")
                            ),
                            value=value,
                        )
                    ),
                )
            raise error

        return StructMethods(
            prev=self.ensure(inner_func), name_to_alias=self.__name_to_alias
        )


class ZangarStruct(TypeSchema[dict], StructMethods[dict]):
    """This is a schema with fields. It can parse any object and return a dict."""

    def _expected_type(self) -> type:
        return object

    def __init__(
        self,
        fields: (
            dict[str, ZangarField]
            | dict[str, SchemaBase]
            | dict[str, ZangarField | SchemaBase]
        ),
        /,
    ):
        self._fields: dict[str, ZangarField] = {}
        for name, field in fields.items():
            if not isinstance(field, ZangarField):
                self._fields[name] = ZangarField(field)
            else:
                self._fields[name] = field

        self._name_to_alias, self._alias_to_name = {}, {}
        for name, field in self._fields.items():
            alias = field._alias or name
            self._name_to_alias[name] = alias
            self._alias_to_name[alias] = name

        super().__init__(name_to_alias=self._name_to_alias)

    def extend(self, fields: dict[str, ZangarField | SchemaBase], /):
        """Extend the struct with additional fields.

        Args:
            fields: The fields to add.
        """
        new_fields: dict[str, ZangarField | SchemaBase] = {}
        new_fields.update(self._fields)
        new_fields.update(fields)
        return self.__class__(new_fields)

    def __check_fieldnames(self, fieldnames: Iterable[str], /):
        for name in fieldnames:
            if name not in self._fields:
                raise ValueError(f"Field {name!r} not found in the struct schema")

    def required_fields(
        self, fieldnames: Iterable[str] | None = None, /
    ) -> ZangarStruct:
        """Make the specified fields required.

        Args:
            fieldnames: The names of the fields to make required.
                If not provided, all fields will be made required.
        """
        if fieldnames is not None:
            self.__check_fieldnames(fieldnames)

        copy_fields: dict[str, ZangarField] = {}
        for name, field in self._fields.items():
            copy_field = copy.copy(field)
            if fieldnames is None or name in fieldnames:
                copy_field.required()
            else:
                copy_field.optional()
            copy_fields[name] = copy_field
        return self.__class__(copy_fields)

    def optional_fields(
        self, fieldnames: Iterable[str] | None = None, /
    ) -> ZangarStruct:
        """Make the specified fields optional.

        Args:
            fieldnames: The names of the fields to make optional.
                If not provided, all fields will be made optional.
        """
        if fieldnames is not None:
            self.__check_fieldnames(fieldnames)
        if fieldnames is None:
            return self.required_fields([])
        return self.required_fields(set(self._fields) - set(fieldnames))

    def pick_fields(self, fieldnames: Iterable[str], /) -> ZangarStruct:
        """Pick the specified fields.

        Args:
            fieldnames: The names of the fields to pick.
        """
        self.__check_fieldnames(fieldnames)
        copy_fields = {}
        for name in fieldnames:
            copy_fields[name] = copy.copy(self._fields[name])
        return self.__class__(copy_fields)

    def omit_fields(self, fieldnames: Iterable[str], /) -> ZangarStruct:
        """Pick all fields except the specified ones.

        Args:
            fieldnames: The names of the fields to omit.
        """
        self.__check_fieldnames(fieldnames)
        return self.pick_fields(set(self._fields) - set(fieldnames))

    def _pretransform(self, value):
        rv = {}
        error = ValidationError()

        for fieldname, field in self._fields.items():
            alias = self._name_to_alias[fieldname]
            if isinstance(value, Mapping):
                try:
                    field_value = getitem(value, alias)
                except KeyError:
                    field_value = _empty
            else:
                try:
                    field_value = getattr(value, alias)
                except AttributeError:
                    field_value = _empty

            if field_value is _empty:
                if field._required:
                    error._set_child_err(
                        alias,
                        ValidationError(
                            get_message(
                                message=(
                                    field._required_message
                                    if field._required_message is not None
                                    else DefaultMessage(name="field_required")
                                ),
                                value=None,
                            )
                        ),
                    )
                    continue

                default = field._get_default()
                if default is not ZangarField._empty:
                    rv[fieldname] = default
            else:
                try:
                    field_value = field.parse(field_value)
                except ValidationError as e:
                    error._set_child_err(alias, e)
                else:
                    rv[fieldname] = field_value

        if not error._empty():
            raise error

        return rv


class ZangarObject(ZangarStruct):
    """Deprecated, use `ZangarStruct` instead."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        warnings.warn(
            "Object is deprecated, use Struct instead",
            DeprecationWarning,
            stacklevel=2,
        )


class ZangarMappingStruct(ZangarStruct):
    """Only support parsing `Mapping` objects.

    Args:
        unknown: The behavior when encountering unknown fields. default to "exclude"

            - "include": Include the unknown fields in the parsed result.

            - "exclude": Exclude the unknown fields from the parsed result.

            - "raise": Raise an error when encountering unknown fields.
    """

    def __init__(
        self,
        *args,
        unknown: t.Literal["include", "exclude", "raise"] = "exclude",
        **kwargs,
    ):
        super().__init__(*args, **kwargs)
        self.__unknown = unknown

    def _expected_type(self) -> type:
        return Mapping

    def _pretransform(self, value):
        assert isinstance(value, Mapping)
        rv = super()._pretransform(value)

        if self.__unknown == "raise":
            error = ValidationError()
            for key in value:
                if key not in self._name_to_alias.values():
                    error._set_child_err(
                        key,
                        ValidationError(
                            get_message(
                                message=DefaultMessage(name="unknown_field"),
                                value=key,
                            )
                        ),
                    )
            if not error._empty():
                raise error

        elif self.__unknown == "include":
            for key in value:
                if key not in self._name_to_alias.values():
                    rv[key] = value[key]
        return rv
