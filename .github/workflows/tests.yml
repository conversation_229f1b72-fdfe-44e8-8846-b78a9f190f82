name: Test Project

on:
  pull_request:

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.8"]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: pip
      - run: pip install tox
      - run: tox run -e py${{ matrix.python-version }}
      - uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
  others:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        env: [style, lint, typing, docbuild]
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: 3.12
          cache: pip
      - run: pip install tox
      - run: tox run -e ${{ matrix.env }}
