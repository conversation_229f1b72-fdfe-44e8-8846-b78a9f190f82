import datetime
import functools
import os
import subprocess


def get_version(version):
    """Return a PEP 440-compliant version number from VERSION."""
    # Now build the two parts of the version number:
    # main = X.Y[.Z]
    # sub = .devN - for pre-alpha releases
    #     | {a|b|rc}N - for alpha, beta, and rc releases

    main = get_main_version(version)

    sub = ""
    if version[3] == "alpha" and version[4] == 0:
        git_changeset = get_git_changeset()
        if git_changeset:
            sub = ".dev%s" % git_changeset

    elif version[3] != "final":
        mapping = {"alpha": "a", "beta": "b", "rc": "rc"}
        sub = mapping[version[3]] + str(version[4])

    return main + sub


def get_main_version(version):
    """Return main version (X.Y[.Z]) from VERSION."""
    parts = 2 if version[2] == 0 else 3
    return ".".join(str(x) for x in version[:parts])


@functools.lru_cache
def get_git_changeset():
    """Return a numeric identifier of the latest git changeset.

    The result is the UTC timestamp of the changeset in YYYYMMDDHHMMSS format.
    This value isn't guaranteed to be unique, but collisions are very unlikely,
    so it's sufficient for generating the development version numbers.
    """
    # Repository may not be found if __file__ is undefined, e.g. in a frozen
    # module.
    if "__file__" not in globals():
        return None  # pragma: no cover
    repo_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    git_log = subprocess.run(
        "git log --pretty=format:%ct --quiet -1 HEAD",
        capture_output=True,
        shell=True,
        cwd=repo_dir,
        text=True,
        check=False,
    )
    tz = datetime.timezone.utc
    try:
        timestamp = datetime.datetime.fromtimestamp(int(git_log.stdout), tz=tz)
    except ValueError:  # pragma: no cover
        return None
    return timestamp.strftime("%Y%m%d%H%M%S")
